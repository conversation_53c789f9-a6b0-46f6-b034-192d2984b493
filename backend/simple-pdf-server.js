require('dotenv').config();
const express = require('express');
const cors = require('cors');
const fs = require('fs');
const { exec } = require('child_process');
// const authMiddleware = require('./src/middlewares/authMiddleware');

const app = express();
const PORT = 3005;

// 中间件
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// 健康检查
app.get('/health', (req, res) => {
    res.json({ status: 'OK', service: 'PDF Service', port: PORT });
});

// 测试路由 - 不需要认证
app.get('/api/pdf/test', (req, res) => {
    res.json({ message: 'wkhtmltopdf路由测试成功', timestamp: new Date().toISOString() });
});

// 极简wkhtmltopdf路由 - 参考腾讯云文章示例（暂时不用认证）
app.post('/api/pdf/generate-wkhtmltopdf', (req, res) => {
    console.log('📄 wkhtmltopdf PDF生成请求');

    const { detectionData, userInfo, documentTitle } = req.body;

    console.log('📊 接收到的数据:', {
        hasDetectionData: !!detectionData,
        hasUserInfo: !!userInfo,
        hasDocumentTitle: !!documentTitle,
        statistics: detectionData?.statistics,
        paragraphsCount: detectionData?.paragraphs?.length
    });

    if (!detectionData || !userInfo || !documentTitle) {
        return res.status(400).json({ success: false, message: '缺少必要参数' });
    }

    // 安全获取统计数据
    const stats = detectionData.statistics || {};
    const totalParagraphs = stats.totalParagraphs || 0;
    const aiParagraphs = stats.aiParagraphs || 0;
    const humanParagraphs = stats.humanParagraphs || 0;
    const aiPercentage = stats.aiPercentage || 0;

    // 生成专业HTML内容 - 使用完整的专业模板
    const htmlContent = generateReportHTML(detectionData, userInfo, documentTitle);

    // 创建临时文件路径
    const timestamp = Date.now();
    const htmlFile = `/tmp/report_${timestamp}.html`;
    const pdfFile = `/tmp/report_${timestamp}.pdf`;

    // 写入HTML文件
    fs.writeFileSync(htmlFile, htmlContent, 'utf8');
    console.log('✅ HTML文件创建:', htmlFile);

    // 构建wkhtmltopdf命令 - 混合方案（最稳定）
    const command = `wkhtmltopdf \
        --page-size A4 \
        --encoding UTF-8 \
        --enable-javascript \
        --enable-external-links \
        --enable-local-file-access \
        --load-error-handling ignore \
        --load-media-error-handling ignore \
        --disable-smart-shrinking \
        --print-media-type \
        --margin-top 15mm \
        --margin-right 15mm \
        --margin-bottom 15mm \
        --margin-left 15mm \
        "${htmlFile}" "${pdfFile}"`;
    console.log('🔄 执行增强的wkhtmltopdf命令:', command);

    // 执行命令
    exec(command, (error) => {
        if (error) {
            console.error(`❌ PDF生成失败: ${error.message}`);
            // 清理文件
            try { fs.unlinkSync(htmlFile); } catch(e) {}
            try { fs.unlinkSync(pdfFile); } catch(e) {}
            return res.status(500).json({ success: false, message: 'PDF生成失败', error: error.message });
        }

        console.log('✅ PDF生成成功');

        // 使用res.download发送文件 - 参考腾讯云文章
        res.download(pdfFile, `AIGC检测报告_${documentTitle}.pdf`, (downloadError) => {
            if (downloadError) {
                console.error(`❌ PDF下载失败: ${downloadError.message}`);
                return res.status(500).json({ success: false, message: 'PDF下载失败' });
            }

            // 清理临时文件
            try { fs.unlinkSync(htmlFile); } catch(e) {}
            try { fs.unlinkSync(pdfFile); } catch(e) {}
            console.log('🎉 PDF发送成功并清理完成');
        });
    });
});

// 生成专业报告HTML模板
function generateReportHTML(detectionData, userInfo, documentTitle) {
    const currentDate = new Date().toLocaleDateString('zh-CN');
    const { paragraphs, statistics, language } = detectionData;

    // 生成带颜色标记的段落
    const coloredText = paragraphs.map((para, index) => {
        const aiPercentage = Math.round((para.ai_probability || 0) * 100);
        let color = '#333';
        let bgColor = '#f8f9fa';
        let borderColor = '#dee2e6';

        if (aiPercentage >= 80) {
            color = '#dc3545';
            bgColor = '#f8d7da';
            borderColor = '#dc3545';
        } else if (aiPercentage >= 50) {
            color = '#fd7e14';
            bgColor = '#fff3cd';
            borderColor = '#fd7e14';
        } else {
            color = '#198754';
            bgColor = '#d1e7dd';
            borderColor = '#198754';
        }

        return `<div style="margin: 15px 0; padding: 12px; border-left: 4px solid ${borderColor}; background: ${bgColor}; border-radius: 4px;">
            <div style="color: ${color}; font-weight: bold; margin-bottom: 8px;">段落${index + 1} - AI生成概率: ${aiPercentage}%</div>
            <div style="color: #333; line-height: 1.6;">${para.text}</div>
        </div>`;
    }).join('');

    // 生成统计图表的SVG
    const chartSVG = generateChartSVG(statistics);

    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AIGC检测报告</title>
    <style>
        body {
            font-family: "SimHei", "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "Noto Sans SC", sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 20px;
            background: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #ff6b35;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
            font-size: 32px;
            font-weight: bold;
        }
        .header p {
            color: #666;
            margin: 0;
            font-size: 16px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section-title {
            color: #333;
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #ff6b35;
        }
        .statistics {
            display: flex;
            justify-content: space-around;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .stat-item {
            text-align: center;
            flex: 1;
        }
        .stat-value {
            display: block;
            font-size: 28px;
            font-weight: bold;
            color: #ff6b35;
            margin-bottom: 5px;
        }
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        .chart-container {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        .detailed-analysis {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 报告头部 -->
        <div class="header">
            <h1>AIGC检测报告</h1>
            <p><strong>文档标题：</strong>${documentTitle}</p>
            <p><strong>检测用户：</strong>${userInfo.username || '未知用户'}</p>
            <p><strong>检测时间：</strong>${currentDate}</p>
            <p><strong>检测语言：</strong>${language === 'zh' ? '中文' : '英文'}</p>
        </div>

        <!-- 统计概览 -->
        <div class="section">
            <h2 class="section-title">📊 检测统计</h2>
            <div class="statistics">
                <div class="stat-item">
                    <span class="stat-value">${statistics.totalParagraphs || 0}</span>
                    <div class="stat-label">总段落数</div>
                </div>
                <div class="stat-item">
                    <span class="stat-value">${statistics.highRiskParagraphs || 0}</span>
                    <div class="stat-label">高风险段落</div>
                </div>
                <div class="stat-item">
                    <span class="stat-value">${statistics.mediumRiskParagraphs || 0}</span>
                    <div class="stat-label">中风险段落</div>
                </div>
                <div class="stat-item">
                    <span class="stat-value">${statistics.lowRiskParagraphs || 0}</span>
                    <div class="stat-label">低风险段落</div>
                </div>
            </div>
        </div>

        <!-- 风险分布图表 -->
        <div class="section">
            <h2 class="section-title">📈 风险分布</h2>
            <div class="chart-container">
                ${chartSVG}
            </div>
        </div>

        <!-- 详细分析 -->
        <div class="section">
            <h2 class="section-title">详细分析</h2>
            <div class="detailed-analysis">
                ${coloredText}
            </div>
        </div>

        <!-- 页脚 -->
        <div class="footer">
            <p>本报告由WriterPro AI检测系统生成 | 生成时间：${new Date().toLocaleString('zh-CN')}</p>
        </div>
    </div>
</body>
</html>`;
}

// 生成图表SVG
function generateChartSVG(statistics) {
    const { highRiskParagraphs, mediumRiskParagraphs, lowRiskParagraphs } = statistics;
    const total = highRiskParagraphs + mediumRiskParagraphs + lowRiskParagraphs;

    if (total === 0) {
        return '<p style="text-align: center; color: #666;">暂无数据</p>';
    }

    // 计算百分比
    const highPercent = (highRiskParagraphs / total * 100).toFixed(1);
    const mediumPercent = (mediumRiskParagraphs / total * 100).toFixed(1);
    const lowPercent = (lowRiskParagraphs / total * 100).toFixed(1);

    // 计算角度
    const highAngle = (highRiskParagraphs / total) * 360;
    const mediumAngle = (mediumRiskParagraphs / total) * 360;
    const lowAngle = (lowRiskParagraphs / total) * 360;

    // 生成SVG饼图
    let currentAngle = 0;
    const radius = 80;
    const centerX = 120;
    const centerY = 120;

    function createArc(startAngle, endAngle, color) {
        const start = polarToCartesian(centerX, centerY, radius, endAngle);
        const end = polarToCartesian(centerX, centerY, radius, startAngle);
        const largeArcFlag = endAngle - startAngle <= 180 ? "0" : "1";

        return `<path d="M ${centerX} ${centerY} L ${start.x} ${start.y} A ${radius} ${radius} 0 ${largeArcFlag} 0 ${end.x} ${end.y} z" fill="${color}"/>`;
    }

    function polarToCartesian(centerX, centerY, radius, angleInDegrees) {
        const angleInRadians = (angleInDegrees - 90) * Math.PI / 180.0;
        return {
            x: centerX + (radius * Math.cos(angleInRadians)),
            y: centerY + (radius * Math.sin(angleInRadians))
        };
    }

    let paths = '';

    // 高风险 - 红色
    if (highRiskParagraphs > 0) {
        paths += createArc(currentAngle, currentAngle + highAngle, '#dc3545');
        currentAngle += highAngle;
    }

    // 中风险 - 橙色
    if (mediumRiskParagraphs > 0) {
        paths += createArc(currentAngle, currentAngle + mediumAngle, '#fd7e14');
        currentAngle += mediumAngle;
    }

    // 低风险 - 绿色
    if (lowRiskParagraphs > 0) {
        paths += createArc(currentAngle, currentAngle + lowAngle, '#198754');
    }

    return `
    <div style="display: flex; align-items: center; justify-content: center; gap: 40px;">
        <svg width="240" height="240" viewBox="0 0 240 240">
            ${paths}
        </svg>
        <div style="text-align: left;">
            <div style="margin-bottom: 10px;">
                <span style="display: inline-block; width: 20px; height: 20px; background: #dc3545; margin-right: 10px; border-radius: 3px;"></span>
                <span>高风险 (≥80%): ${highRiskParagraphs}段 (${highPercent}%)</span>
            </div>
            <div style="margin-bottom: 10px;">
                <span style="display: inline-block; width: 20px; height: 20px; background: #fd7e14; margin-right: 10px; border-radius: 3px;"></span>
                <span>中风险 (50-80%): ${mediumRiskParagraphs}段 (${mediumPercent}%)</span>
            </div>
            <div style="margin-bottom: 10px;">
                <span style="display: inline-block; width: 20px; height: 20px; background: #198754; margin-right: 10px; border-radius: 3px;"></span>
                <span>低风险 (<50%): ${lowRiskParagraphs}段 (${lowPercent}%)</span>
            </div>
        </div>
    </div>`;
}

// 启动服务器
app.listen(PORT, () => {
    console.log('📄 简化PDF服务器运行在端口', PORT);
    console.log('🔗 访问地址: http://localhost:' + PORT);
});
