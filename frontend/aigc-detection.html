<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AIGC查重检测 - WriterPro</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="apple-style.css">
    <link rel="stylesheet" href="minimal-footer.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        /* 苹果风格 - 极致简洁设计 */
        .aigc-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }



        /* 页面头部 - 苹果式大标题 */
        .page-header {
            text-align: center;
            padding: 4rem 0 6rem 0;
            background: linear-gradient(180deg, rgba(255,255,255,0) 0%, rgba(247,247,247,0.3) 100%);
        }

        .page-title {
            font-size: clamp(2.5rem, 5vw, 4rem);
            font-weight: 700;
            color: #1d1d1f;
            margin-bottom: 1.5rem;
            letter-spacing: -0.02em;
            line-height: 1.1;
        }

        .page-subtitle {
            font-size: 1.375rem;
            color: #86868b;
            font-weight: 400;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.4;
        }

        /* 主内容区 - 极简布局 */
        .main-content {
            display: grid;
            grid-template-columns: 1fr 320px;
            gap: 3rem;
            margin-bottom: 6rem;
            align-items: start;
        }

        .detection-panel {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.18);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
        }

        /* 输入区域 - 苹果式简洁 */
        .input-section {
            margin-bottom: 3rem;
        }

        .input-label {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 1.5rem;
            display: block;
            letter-spacing: -0.01em;
        }

        /* CSS变量定义 */
        :root {
            --color-primary-orange: #FF6B35;
            --color-background-card: rgba(255, 255, 255, 0.8);
            --color-border-light: rgba(0, 0, 0, 0.1);
            --color-text-dark: #1d1d1f;
            --color-text-medium: #86868b;
            --color-gradient-orange: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;
            --radius-md: 12px;
            --radius-lg: 16px;
            --font-size-body: 1rem;
            --transition-ease: all 0.3s ease;
            --shadow-glass-lg: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        /* 标签页样式 */
        .tab-container {
            margin-bottom: var(--spacing-md);
        }

        .tab-buttons {
            display: flex;
            margin-bottom: var(--spacing-md);
            border-bottom: 1px solid var(--color-border-light);
        }

        .tab-button {
            flex: 1;
            padding: var(--spacing-sm) var(--spacing-md);
            background-color: transparent;
            border: none;
            border-bottom: 3px solid transparent;
            color: var(--color-text-medium);
            font-size: var(--font-size-body);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .tab-button:hover {
            color: var(--color-primary-orange);
        }

        .tab-button.active {
            color: var(--color-primary-orange);
            border-color: var(--color-primary-orange);
        }

        .tab-content {
            position: relative;
            min-height: 250px;
        }

        .tab-pane {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            opacity: 0;
            transform: translateY(10px);
            transition: opacity 0.3s ease, transform 0.3s ease;
            pointer-events: none;
        }

        .tab-pane.active {
            opacity: 1;
            transform: translateY(0);
            pointer-events: all;
            position: static;
        }

        /* 上传区域样式 */
        .upload-area {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-xl);
            border: 2px dashed var(--color-border-light);
            border-radius: var(--radius-lg);
            cursor: pointer;
            transition: var(--transition-ease);
            background-color: rgba(255, 107, 53, 0.03);
            min-height: 250px;
        }

        .upload-area:hover {
            background-color: rgba(255, 107, 53, 0.08);
        }

        .upload-area.has-file {
            background-color: rgba(82, 196, 26, 0.05);
            border-color: rgba(82, 196, 26, 0.3);
        }

        .upload-area .upload-icon {
            width: 64px;
            height: 64px;
            background: var(--color-gradient-orange);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: var(--spacing-md);
            color: white;
        }

        .upload-area h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--color-text-dark);
            margin-bottom: var(--spacing-sm);
        }

        .upload-area p {
            font-size: var(--font-size-body);
            color: var(--color-text-medium);
            margin-bottom: var(--spacing-lg);
        }

        .upload-btn {
            background: var(--color-gradient-orange);
            color: white;
            border: none;
            padding: var(--spacing-sm) var(--spacing-xl);
            border-radius: var(--radius-md);
            font-size: var(--font-size-body);
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition-ease);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-xs);
            margin-bottom: var(--spacing-xl);
        }

        .upload-btn:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }

        /* 文本输入框样式 */
        #inputText {
            width: 100%;
            min-height: 250px;
            padding: var(--spacing-md);
            font-size: var(--font-size-body);
            line-height: 1.2;
            resize: vertical;
            color: var(--color-text-dark);
            background-color: var(--color-background-card);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--color-border-light);
            border-radius: var(--radius-lg);
            margin-left: -20px;
        }

        #inputText:focus {
            outline: none;
            border-color: var(--color-primary-orange);
            box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.2);
        }

        /* 特性展示 */
        .upload-features {
            display: flex;
            justify-content: space-around;
            margin-top: var(--spacing-lg);
            padding-top: var(--spacing-lg);
            border-top: 1px solid var(--color-border-light);
        }

        .feature-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-xs);
            color: var(--color-text-medium);
            font-size: 0.875rem;
        }

        .feature-item svg {
            color: var(--color-primary-orange);
        }

        /* 控制区域 */
        .controls {
            display: flex;
            gap: var(--spacing-md);
            margin-top: var(--spacing-lg);
        }

        .control-group {
            flex: 1;
        }

        .control-group label {
            display: block;
            margin-bottom: var(--spacing-xs);
            font-weight: 500;
            color: var(--color-text-dark);
        }

        #optimizeType {
            width: 100%;
            padding: var(--spacing-sm) var(--spacing-md);
            border: 1px solid var(--color-border-light);
            border-radius: var(--radius-md);
            background-color: var(--color-background-card);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            color: var(--color-text-dark);
            font-size: var(--font-size-body);
        }

        .input-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 1rem;
            font-size: 0.875rem;
            color: #86868b;
            font-weight: 400;
        }

        /* 文件上传 - 苹果式优雅 */
        .file-upload-area {
            border: 2px dashed rgba(0, 0, 0, 0.1);
            border-radius: 16px;
            padding: 3rem 2rem;
            text-align: center;
            margin-bottom: 2rem;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            cursor: pointer;
            background: rgba(255, 255, 255, 0.5);
        }

        .file-upload-area:hover {
            border-color: #007AFF;
            background: rgba(0, 122, 255, 0.02);
            transform: translateY(-2px);
        }

        .file-upload-area.dragover {
            border-color: #007AFF;
            background: rgba(0, 122, 255, 0.05);
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            opacity: 0.6;
        }

        .upload-text {
            font-size: 1.125rem;
            font-weight: 500;
            color: #1d1d1f;
            margin-bottom: 0.5rem;
        }

        .upload-hint {
            font-size: 0.875rem;
            color: #86868b;
        }

        /* 按钮 - 苹果式设计 */
        .control-buttons {
            display: flex;
            gap: 1rem;
            margin-bottom: 3rem;
        }

        .btn {
            padding: 1rem 2rem;
            border-radius: 12px;
            font-weight: 500;
            text-decoration: none;
            border: none;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            font-size: 1rem;
            letter-spacing: -0.01em;
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: #007AFF;
            color: white;
            box-shadow: 0 4px 14px rgba(0, 122, 255, 0.25);
        }

        .btn-primary:hover {
            background: #0056CC;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 122, 255, 0.35);
        }

        .btn-primary:active {
            transform: translateY(0);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.8);
            color: #1d1d1f;
            border: 1px solid rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.95);
            transform: translateY(-1px);
            box-shadow: 0 4px 14px rgba(0, 0, 0, 0.1);
        }

        /* 结果展示 - 苹果式可视化 */
        .result-section {
            margin-top: 3rem;
            padding: 3rem;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(247, 247, 247, 0.9) 100%);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.18);
            display: none;
            animation: fadeInUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .result-section.show {
            display: block;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .ai-probability {
            text-align: center;
            margin-bottom: 3rem;
        }

        .probability-circle {
            width: 160px;
            height: 160px;
            margin: 0 auto 2rem;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .probability-ring {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: conic-gradient(from 0deg, #007AFF 0%, #007AFF var(--percentage), rgba(0, 122, 255, 0.1) var(--percentage), rgba(0, 122, 255, 0.1) 100%);
            padding: 8px;
            animation: rotateIn 1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .probability-inner {
            width: 100%;
            height: 100%;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        @keyframes rotateIn {
            from {
                transform: rotate(-90deg);
                opacity: 0;
            }
            to {
                transform: rotate(0deg);
                opacity: 1;
            }
        }

        .probability-text {
            font-size: 2rem;
            font-weight: 700;
            color: #1d1d1f;
            letter-spacing: -0.02em;
        }

        .detection-conclusion {
            font-size: 1.375rem;
            font-weight: 600;
            margin-bottom: 1rem;
            letter-spacing: -0.01em;
        }

        .result-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 2rem;
        }

        /* 侧边栏 - 极简卡片 */
        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .info-card {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.18);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 1.5rem;
            letter-spacing: -0.01em;
        }

        /* 企业服务 - 苹果式渐变 */
        .enterprise-section {
            background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
            color: white;
            margin: 6rem 0;
            padding: 4rem 3rem;
            border-radius: 24px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .enterprise-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }

        .enterprise-title {
            font-size: clamp(1.75rem, 4vw, 2.5rem);
            font-weight: 700;
            margin-bottom: 1.5rem;
            letter-spacing: -0.02em;
            position: relative;
        }

        .enterprise-subtitle {
            font-size: 1.25rem;
            margin-bottom: 2.5rem;
            opacity: 0.9;
            font-weight: 400;
            position: relative;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .sidebar {
                order: -1;
            }
        }

        @media (max-width: 768px) {
            .aigc-container {
                padding: 0 1rem;
            }

            .page-header {
                padding: 2rem 0 3rem 0;
            }

            .detection-panel {
                padding: 2rem;
            }

            .control-buttons {
                flex-direction: column;
            }

            .btn {
                width: 100%;
            }
        }

        /* 历史记录样式优化 */
        .history-item {
            padding: 1rem 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.2s ease;
        }

        .history-item:hover {
            background: rgba(0, 122, 255, 0.02);
            border-radius: 8px;
            padding: 1rem;
            margin: 0 -1rem;
        }

        .history-text {
            font-size: 0.9rem;
            color: #1d1d1f;
            font-weight: 400;
            line-height: 1.4;
            margin-bottom: 0.5rem;
        }

        .history-meta {
            font-size: 0.8rem;
            color: #86868b;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .probability-badge {
            background: rgba(0, 122, 255, 0.1);
            color: #007AFF;
            padding: 0.25rem 0.5rem;
            border-radius: 6px;
            font-weight: 500;
        }

        /* 用户菜单样式 - 与首页保持一致 */
        .user-menu {
            position: relative;
            display: inline-block;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .user-info:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
            box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
        }

        .username {
            color: white;
            font-weight: 500;
            font-size: 14px;
        }

        .dropdown-arrow {
            color: white;
            transition: transform 0.3s ease;
        }

        .user-info:hover .dropdown-arrow {
            transform: rotate(180deg);
        }

        .user-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            min-width: 160px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            z-index: 1000;
            margin-top: 8px;
        }

        .user-menu:hover .user-dropdown {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 16px;
            cursor: pointer;
            transition: background-color 0.2s ease;
            color: #333;
            font-size: 14px;
        }

        .dropdown-item:hover {
            background-color: #f5f5f5;
        }

        .dropdown-item:first-child {
            border-radius: 8px 8px 0 0;
        }

        .dropdown-item:last-child {
            border-radius: 0 0 8px 8px;
        }

        .dropdown-item svg {
            width: 16px;
            height: 16px;
            color: #666;
        }

        /* 语言选择器样式 */
        .language-selector {
            display: flex;
            gap: 20px;
            margin-top: 8px;
        }

        .language-option {
            display: flex;
            align-items: center;
            gap: 6px;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 6px;
            transition: all 0.2s ease;
            border: 1px solid #e0e0e0;
            background: #fff;
        }

        .language-option:hover {
            background: #f8f9fa;
            border-color: #ff6b35;
        }

        .language-option input[type="radio"] {
            margin: 0;
            width: 16px;
            height: 16px;
            accent-color: #ff6b35;
        }

        .language-option input[type="radio"]:checked + .language-text {
            color: #ff6b35;
            font-weight: 500;
        }

        .language-option:has(input[type="radio"]:checked) {
            background: #fff5f2;
            border-color: #ff6b35;
        }

        .language-text {
            font-size: 14px;
            color: #333;
            transition: color 0.2s ease;
        }
    </style>
</head>
<body data-page="aigc-detection">
    <div id="notificationContainer"></div>
    <nav class="navbar">
        <div class="navbar-brand">
            <svg class="logo" width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
            <h1>WriterPro</h1>
        </div>
        <ul class="navbar-nav" id="navbarNav">
            <li><a href="index.html" class="nav-link">首页</a></li>
            <li><a href="aigc-detection.html" class="nav-link active">查重永久免费</a></li>
            <li><a href="history.html" class="nav-link">历史记录</a></li>
            <li><a href="partnership.html" class="nav-link">招商加盟</a></li>
            <li><a href="#" class="nav-link" id="contactServiceBtn">联系客服</a></li>
        </ul>
        <div class="navbar-auth">
            <a href="login.html" id="loginBtn" class="auth-btn">登录 / 注册</a>
            <div class="user-menu" id="userMenu" style="display: none;">
                <div class="user-info" id="userMenuTrigger">
                    <div class="user-avatar">
                        <span id="userInitial">U</span>
                    </div>
                    <span class="username" id="navUsername">用户</span>
                    <svg class="dropdown-arrow" width="12" height="12" viewBox="0 0 24 24" fill="none">
                        <path d="M6 9l6 6 6-6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <div class="user-dropdown" id="userDropdown">
                    <div class="dropdown-item" id="changePasswordBtn">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <path d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        修改密码
                    </div>
                    <div class="dropdown-item" id="logoutBtn">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <path d="M9 21H5a2 2 0 01-2-2V5a2 2 0 012-2h4m7 14l5-5-5-5m5 5H9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        退出登录
                    </div>
                </div>
            </div>
        </div>
        <button class="mobile-menu-toggle" id="mobileMenuToggle" aria-label="切换菜单">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 12h18M3 6h18M3 18h18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
        </button>
    </nav>

    <main class="aigc-container">


        <!-- 页面头部 -->
        <header class="page-header">
            <h1 class="page-title">AI内容检测</h1>
            <p class="page-subtitle">智能识别AI生成内容，支持中英文检测，永久免费使用</p>
        </header>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 检测面板 -->
            <div class="detection-panel">
                <!-- 输入区域 -->
                <div class="input-section card">
                    <div class="tab-container">
                        <div class="tab-buttons">
                            <button class="tab-button active" data-tab="upload">上传文档</button>
                            <button class="tab-button" data-tab="text">粘贴文本</button>
                        </div>
                        <div class="tab-content">
                            <div id="uploadTabContent" class="tab-pane active" data-tab="upload">
                                <div class="upload-area">
                                    <input type="file" id="documentUpload" accept=".txt,.doc,.docx" style="display: none;">
                                    <div class="upload-icon">
                                        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            <polyline points="7,10 12,15 17,10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                        </svg>
                                    </div>
                                    <h3>文档上传区域</h3>
                                    <p>支持 .txt, .docx, 等格式</p>
                                    <button type="button" class="upload-btn" id="uploadBtn">选择文件</button>
                                </div>
                                <div class="upload-features">
                                    <div class="feature-item">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path></svg>
                                        <span>智能识别</span>
                                    </div>
                                    <div class="feature-item">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path></svg>
                                        <span>永久免费</span>
                                    </div>
                                    <div class="feature-item">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="2" y1="12" x2="22" y2="12"></line><path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path></svg>
                                        <span>中英文支持</span>
                                    </div>
                                </div>
                            </div>
                            <div id="textTabContent" class="tab-pane" data-tab="text">
                                <textarea id="inputText" placeholder="请在此输入要检测的文本内容..."></textarea>
                                <div class="upload-features">
                                    <div class="feature-item">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path></svg>
                                        <span>智能识别</span>
                                    </div>
                                    <div class="feature-item">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path></svg>
                                        <span>永久免费</span>
                                    </div>
                                    <div class="feature-item">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="2" y1="12" x2="22" y2="12"></line><path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path></svg>
                                        <span>中英文支持</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 检测模式选择 -->
                    <div class="controls">
                        <div class="control-group">
                            <label for="detectionLanguage">检测模式：</label>
                            <div class="language-selector">
                                <label class="language-option">
                                    <input type="radio" id="lang-zh" name="detectionLanguage" value="zh" checked>
                                    <span class="language-text">中文</span>
                                </label>
                                <label class="language-option">
                                    <input type="radio" id="lang-en" name="detectionLanguage" value="en">
                                    <span class="language-text">英文</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 控制按钮 -->
                <div class="control-buttons">
                    <button class="btn btn-primary" id="detectBtn">
                        <span id="detectBtnText">开始检测</span>
                    </button>
                    <button class="btn btn-secondary" id="clearBtn">清空内容</button>
                    <button class="btn btn-secondary" id="sampleBtn">示例文本</button>
                </div>

                <!-- 结果展示区 -->
                <div class="result-section" id="resultSection">
                    <div class="ai-probability">
                        <div class="probability-circle" id="probabilityCircle">
                            <div class="probability-ring" style="--percentage: 0%">
                                <div class="probability-inner">
                                    <div class="probability-text" id="probabilityText">0%</div>
                                </div>
                            </div>
                        </div>
                        <h3 class="detection-conclusion" id="detectionConclusion">检测结果</h3>
                        <div id="detectionDetails" style="color: #86868b; font-size: 1rem; margin-top: 0.5rem;"></div>
                    </div>

                    <div class="result-actions">
                        <button class="btn btn-secondary" id="downloadBtn">
                            <span>📄</span> 下载报告
                        </button>
                        <button class="btn btn-primary" id="optimizeBtn">
                            <span>✨</span> 优化AI率
                        </button>
                    </div>
                </div>
            </div>

            <!-- 侧边栏 -->
            <div class="sidebar">
                <!-- 使用说明 -->
                <div class="info-card">
                    <h3 class="card-title">使用说明</h3>
                    <ul style="line-height: 2; color: #86868b; font-size: 0.9rem; list-style: none; padding: 0;">
                        <li style="margin-bottom: 0.75rem;">✓ 支持中英文内容检测</li>
                        <li style="margin-bottom: 0.75rem;">✓ 可上传 .txt 和 .docx 文件</li>
                        <li style="margin-bottom: 0.75rem;">✓ 检测结果实时显示</li>
                        <li style="margin-bottom: 0.75rem;">✓ 永久免费使用</li>
                    </ul>
                </div>

                <!-- 检测历史 -->
                <div class="info-card">
                    <h3 class="card-title">最近检测</h3>
                    <div id="recentHistory">
                        <p style="color: #86868b; text-align: center; font-size: 0.9rem;">暂无检测记录</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 企业查重服务 -->
        <section class="enterprise-section">
            <h2 class="enterprise-title">企业查重服务</h2>
            <p class="enterprise-subtitle">为企业提供专业的批量检测和定制化服务</p>
            <button class="btn btn-secondary" style="background: white; color: #FF6B35;">联系我们</button>
        </section>
    </main>

    <!-- 引入JSZip库用于DOCX文件解析 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>

    <script>
        // 标签页切换功能
        document.addEventListener('DOMContentLoaded', function() {
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabPanes = document.querySelectorAll('.tab-pane');

            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const targetTab = this.getAttribute('data-tab');

                    // 移除所有活动状态
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabPanes.forEach(pane => pane.classList.remove('active'));

                    // 添加活动状态
                    this.classList.add('active');
                    document.getElementById(targetTab + 'TabContent').classList.add('active');
                });
            });

            // 文件上传功能已移除，统一使用script.js中的处理逻辑

            // 拖拽支持已移除，统一使用script.js中的处理逻辑

            // 文件处理函数已移除，统一使用script.js中的处理逻辑

            // 所有辅助函数已移除，统一使用script.js中的处理逻辑


        });
    </script>

    <!-- 引入现有的脚本 -->
    <script src="script.js"></script>
    <script src="aigc-detection.js"></script>

    <!-- 报告生成所需的库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- PDFMake 库 (主方案) -->
    <script src="https://cdn.jsdelivr.net/npm/pdfmake@latest/build/pdfmake.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/pdfmake@latest/build/vfs_fonts.min.js"></script>

    <!-- html-to-pdfmake 库 (主方案) -->
    <script src="https://cdn.jsdelivr.net/npm/html-to-pdfmake/browser.js"></script>

    <!-- html2pdf.js 库 (备用方案) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.9.3/html2pdf.bundle.min.js"></script>
</body>
</html>
