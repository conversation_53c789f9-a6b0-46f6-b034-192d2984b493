// AIGC检测页面JavaScript
class AigcDetection {
    constructor() {
        this.apiBaseUrl = 'https://writerpro.cn/api/aigc';
        this.isDetecting = false;
        this.manualLanguageSelection = false; // 是否手动选择了语言
        this.init();
    }

    init() {
        this.initAuthManager();
        this.bindEvents();
        this.loadRecentHistory();
        this.updateCharCount();
        this.updateUIBasedOnAuthState();
        this.initLanguageSelector();
        this.initReportGeneration();
    }

    initAuthManager() {
        // 等待AuthManager准备就绪
        if (window.AuthManager) {
            // 强制重新初始化AuthManager
            window.AuthManager._initialized = false;
            window.AuthManager.init();
            console.log('AIGC页面 - AuthManager重新初始化完成，登录状态:', window.AuthManager.isLoggedIn());
        } else {
            console.warn('AIGC页面 - AuthManager未找到，可能影响登录状态同步');
        }

        // 监听localStorage变化，实时更新UI状态
        window.addEventListener('storage', (e) => {
            if (e.key === 'token') {
                console.log('AIGC页面 - 检测到token变化，更新UI状态');
                // 同步AuthManager的状态
                if (window.AuthManager) {
                    window.AuthManager._token = e.newValue;
                }
                this.updateUIBasedOnAuthState();
            }
        });

        // 定期检查token状态（防止同页面内的token变化）
        setInterval(() => {
            if (window.AuthManager) {
                const isLoggedIn = window.AuthManager.isLoggedIn();
                const userMenu = document.getElementById('userMenu');
                const uiShowsLoggedIn = userMenu && userMenu.style.display !== 'none';

                if (isLoggedIn !== uiShowsLoggedIn) {
                    console.log('AIGC页面 - 检测到token状态与UI不一致，更新UI');
                    this.updateUIBasedOnAuthState();
                }
            }
        }, 1000);
    }

    updateUIBasedOnAuthState() {
        const isLoggedIn = window.AuthManager && window.AuthManager.isLoggedIn();

        // 更新导航栏登录状态
        const loginBtn = document.getElementById('loginBtn');
        const userMenu = document.getElementById('userMenu');

        if (isLoggedIn) {
            if (loginBtn) loginBtn.style.display = 'none';
            if (userMenu) userMenu.style.display = 'block';
            this.updateNavUserInfo();
        } else {
            if (loginBtn) loginBtn.style.display = 'block';
            if (userMenu) userMenu.style.display = 'none';
        }
    }

    async updateNavUserInfo() {
        if (!window.AuthManager || !window.AuthManager.isLoggedIn()) return;

        try {
            const token = window.AuthManager.getToken();
            const response = await fetch('/api/auth/profile', {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const responseData = await response.json();
                // 处理 {success: true, user: {...}} 格式的响应
                const userData = responseData.user || responseData;
                const navUsername = document.getElementById('navUsername');
                const userInitial = document.getElementById('userInitial');

                if (navUsername && userData) {
                    navUsername.textContent = userData.username || userData.email || '用户';
                }

                if (userInitial && userData) {
                    const username = userData.username || userData.email || '用户';
                    userInitial.textContent = username.charAt(0).toUpperCase();
                }
            }
        } catch (error) {
            console.error('获取用户信息失败:', error);
        }
    }

    bindEvents() {
        // 文本输入事件
        const textInput = document.getElementById('inputText');
        if (textInput) {
            textInput.addEventListener('input', () => this.updateCharCount());
        }

        // 文件上传事件处理已移除，统一使用script.js中的处理逻辑

        // 按钮事件 - 添加安全检查
        const detectBtn = document.getElementById('detectBtn');
        const clearBtn = document.getElementById('clearBtn');
        const sampleBtn = document.getElementById('sampleBtn');
        const downloadBtn = document.getElementById('downloadBtn');
        const optimizeBtn = document.getElementById('optimizeBtn');

        if (detectBtn) {
            detectBtn.addEventListener('click', () => this.startDetection());
        }
        if (clearBtn) {
            clearBtn.addEventListener('click', () => this.clearContent());
        }
        if (sampleBtn) {
            sampleBtn.addEventListener('click', () => this.loadSampleText());
        }
        if (downloadBtn) {
            downloadBtn.addEventListener('click', () => this.downloadReport());
        }
        if (optimizeBtn) {
            optimizeBtn.addEventListener('click', () => this.optimizeContent());
        }

        // 退出登录按钮
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleLogout();
            });
        }

        // 修改密码按钮
        const changePasswordBtn = document.getElementById('changePasswordBtn');
        if (changePasswordBtn) {
            changePasswordBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleChangePassword();
            });
        }
    }

    handleLogout() {
        if (window.AuthManager) {
            window.AuthManager.clearToken();
            this.updateUIBasedOnAuthState();
            this.showNotification('已退出登录', 'success');
        }
    }

    handleChangePassword() {
        // 使用主页的修改密码功能
        if (window.showPasswordModal) {
            window.showPasswordModal();
        } else {
            // 如果主页的函数不可用，跳转到主页
            window.location.href = 'index.html';
        }
    }

    updateCharCount() {
        // 新的HTML结构中暂时没有字符计数显示
        // 如果需要可以在控制台显示字符数
        const textInput = document.getElementById('inputText');
        if (textInput) {
            const count = textInput.value.length;
            console.log(`当前字符数: ${count}`);
        }
    }

    // 文件处理方法已移除，统一使用script.js中的处理逻辑

    // processFile方法已移除，统一使用script.js中的处理逻辑

    // 所有文件读取方法已移除，统一使用script.js中的处理逻辑

    initLanguageSelector() {
        const inputText = document.getElementById('inputText');
        const langZh = document.getElementById('lang-zh');
        const langEn = document.getElementById('lang-en');

        // 监听语言选择器的手动切换
        if (langZh && langEn) {
            langZh.addEventListener('change', () => {
                if (langZh.checked) {
                    this.manualLanguageSelection = true;
                    console.log('手动选择中文模式');
                }
            });

            langEn.addEventListener('change', () => {
                if (langEn.checked) {
                    this.manualLanguageSelection = true;
                    console.log('手动选择英文模式');
                }
            });
        }

        // 监听文本输入，自动检测语言
        if (inputText) {
            inputText.addEventListener('input', () => {
                if (!this.manualLanguageSelection) {
                    this.autoDetectLanguage(inputText.value);
                }
            });
        }
    }

    autoDetectLanguage(text) {
        if (!text || text.length < 10) return;

        // 检测中文字符比例
        const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
        const totalChars = text.replace(/\s/g, '').length;
        const chineseRatio = totalChars > 0 ? chineseChars / totalChars : 0;

        const langZh = document.getElementById('lang-zh');
        const langEn = document.getElementById('lang-en');

        if (chineseRatio > 0.3) {
            // 中文字符超过30%，选择中文模式
            if (langZh) langZh.checked = true;
            console.log(`自动检测为中文模式 (中文比例: ${(chineseRatio * 100).toFixed(1)}%)`);
        } else {
            // 否则选择英文模式
            if (langEn) langEn.checked = true;
            console.log(`自动检测为英文模式 (中文比例: ${(chineseRatio * 100).toFixed(1)}%)`);
        }
    }

    getSelectedLanguage() {
        const langZh = document.getElementById('lang-zh');
        const langEn = document.getElementById('lang-en');

        if (langZh && langZh.checked) return 'zh';
        if (langEn && langEn.checked) return 'en';
        return 'zh'; // 默认中文
    }

    async startDetection() {
        const textInput = document.getElementById('inputText');
        const text = textInput ? textInput.value.trim() : '';

        if (!text) {
            this.showNotification('请输入要检测的文本内容', 'warning');
            return;
        }

        if (text.length < 10) {
            this.showNotification('文本长度至少需要10个字符', 'warning');
            return;
        }

        // 检查登录状态 - 使用统一的AuthManager
        if (!window.AuthManager || !window.AuthManager.isLoggedIn()) {
            this.showNotification('请先登录后再使用检测功能', 'warning');
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 2000);
            return;
        }

        const token = window.AuthManager.getToken();

        this.setDetecting(true);

        try {
            const selectedLanguage = this.getSelectedLanguage();
            console.log(`发送检测请求，语言: ${selectedLanguage}`);

            const response = await fetch(`${this.apiBaseUrl}/detect`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({
                    text: text,
                    language: selectedLanguage
                })
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || '检测失败');
            }

            this.displayResult(data);
            this.saveToHistory(text, data);
            this.loadRecentHistory();

        } catch (error) {
            console.error('检测错误:', error);
            this.showNotification(error.message || '检测服务暂时不可用，请稍后重试', 'error');
        } finally {
            this.setDetecting(false);
        }
    }

    setDetecting(detecting) {
        this.isDetecting = detecting;
        const detectBtn = document.getElementById('detectBtn');
        const detectBtnText = document.getElementById('detectBtnText');

        if (detecting) {
            detectBtn.disabled = true;
            detectBtnText.textContent = '检测中...';
            detectBtn.style.opacity = '0.7';
        } else {
            detectBtn.disabled = false;
            detectBtnText.textContent = '开始检测';
            detectBtn.style.opacity = '1';
        }
    }

    displayResult(data) {
        const resultSection = document.getElementById('resultSection');
        const probabilityText = document.getElementById('probabilityText');
        const detectionConclusion = document.getElementById('detectionConclusion');
        const detectionDetails = document.getElementById('detectionDetails');
        const probabilityRing = document.querySelector('.probability-ring');

        // 显示结果区域
        resultSection.classList.add('show');

        // 显示AI概率
        const aiProbability = Math.round((data.ai_probability || 0) * 100);

        // 动画更新概率显示
        this.animatePercentage(0, aiProbability, (value) => {
            probabilityText.textContent = `${value}%`;
            probabilityRing.style.setProperty('--percentage', `${value * 3.6}deg`);
        });

        // 显示检测结论 - 统一显示AI率整数百分比
        let conclusionColor = '';
        let ringColor = '';

        if (aiProbability < 30) {
            conclusionColor = '#34C759';
            ringColor = '#34C759';
        } else if (aiProbability < 70) {
            conclusionColor = '#FF9500';
            ringColor = '#FF9500';
        } else {
            conclusionColor = '#FF3B30';
            ringColor = '#FF3B30';
        }

        // 统一显示格式：AI率 XX%
        detectionConclusion.textContent = `AI率 ${aiProbability}%`;
        detectionConclusion.style.color = conclusionColor;
        detectionDetails.textContent = ''; // 不显示详细描述

        // 更新圆环颜色
        setTimeout(() => {
            probabilityRing.style.background = `conic-gradient(from 0deg, ${ringColor} 0%, ${ringColor} ${aiProbability * 3.6}deg, rgba(0, 0, 0, 0.1) ${aiProbability * 3.6}deg, rgba(0, 0, 0, 0.1) 100%)`;
        }, 1000);

        this.showNotification('检测完成', 'success');
    }

    animatePercentage(start, end, callback) {
        const duration = 1500;
        const startTime = performance.now();

        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // 使用缓动函数
            const easeOutCubic = 1 - Math.pow(1 - progress, 3);
            const currentValue = Math.round(start + (end - start) * easeOutCubic);

            callback(currentValue);

            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        requestAnimationFrame(animate);
    }

    clearContent() {
        const inputText = document.getElementById('inputText');
        const resultSection = document.getElementById('resultSection');

        if (inputText) {
            inputText.value = '';
        }
        if (resultSection) {
            resultSection.classList.remove('show');
        }
        this.updateCharCount();
    }

    loadSampleText() {
        const sampleText = `人工智能技术的快速发展正在改变我们的生活方式。从智能手机到自动驾驶汽车，AI技术已经渗透到各个领域。机器学习算法能够分析大量数据，识别模式，并做出预测。深度学习网络模拟人脑神经元的工作方式，在图像识别、自然语言处理等任务中表现出色。然而，AI技术的发展也带来了一些挑战，包括就业影响、隐私保护和伦理问题。我们需要在推进技术创新的同时，确保AI的发展符合人类的最佳利益。`;

        const inputText = document.getElementById('inputText');
        if (inputText) {
            inputText.value = sampleText;
            this.updateCharCount();
        }
    }

    downloadReport() {
        // 检查是否有检测结果
        const resultSection = document.getElementById('resultSection');
        if (!resultSection || !resultSection.classList.contains('show')) {
            this.showNotification('请先进行AIGC检测', 'warning');
            return;
        }

        // 开始生成详细报告
        this.generateDetailedReport();
    }

    optimizeContent() {
        // 跳转到主页的优化功能
        window.location.href = 'index.html';
    }

    saveToHistory(text, result) {
        try {
            const history = JSON.parse(localStorage.getItem('aigcHistory') || '[]');
            const record = {
                id: Date.now(),
                text: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
                aiProbability: result.ai_probability,
                timestamp: new Date().toISOString(),
                conclusion: result.prediction || 'unknown'
            };
            
            history.unshift(record);
            // 只保留最近20条记录
            if (history.length > 20) {
                history.splice(20);
            }
            
            localStorage.setItem('aigcHistory', JSON.stringify(history));
        } catch (error) {
            console.error('保存历史记录失败:', error);
        }
    }

    loadRecentHistory() {
        try {
            const history = JSON.parse(localStorage.getItem('aigcHistory') || '[]');
            const recentHistory = document.getElementById('recentHistory');

            if (history.length === 0) {
                recentHistory.innerHTML = '<p style="color: #86868b; text-align: center; font-size: 0.9rem;">暂无检测记录</p>';
                return;
            }

            const recentItems = history.slice(0, 2);
            recentHistory.innerHTML = recentItems.map(item => {
                const date = new Date(item.timestamp).toLocaleDateString('zh-CN', {
                    month: 'short',
                    day: 'numeric'
                });
                const probability = Math.round((item.aiProbability || 0) * 100);

                let badgeColor = '#34C759';
                if (probability >= 70) badgeColor = '#FF3B30';
                else if (probability >= 30) badgeColor = '#FF9500';

                return `
                    <div class="history-item">
                        <div class="history-text">${item.text}</div>
                        <div class="history-meta">
                            <span>${date}</span>
                            <span class="probability-badge" style="background: ${badgeColor}20; color: ${badgeColor};">
                                ${probability}%
                            </span>
                        </div>
                    </div>
                `;
            }).join('');
        } catch (error) {
            console.error('加载历史记录失败:', error);
        }
    }

    showNotification(message, type = 'info') {
        // 使用现有的通知系统
        if (window.showNotification) {
            window.showNotification(message, type);
        } else {
            alert(message);
        }
    }

    // 初始化报告生成功能
    initReportGeneration() {
        // 确保库已加载
        this.checkLibrariesLoaded();
    }

    checkLibrariesLoaded() {
        const checkInterval = setInterval(() => {
            if (typeof Chart !== 'undefined' && typeof html2pdf !== 'undefined') {
                console.log('✅ 报告生成库已加载完成');
                clearInterval(checkInterval);
            }
        }, 100);

        // 10秒后停止检查
        setTimeout(() => {
            clearInterval(checkInterval);
            if (typeof Chart === 'undefined' || typeof html2pdf === 'undefined') {
                console.warn('⚠️ 报告生成库加载失败，报告功能可能不可用');
            }
        }, 10000);
    }

    // 生成详细报告
    async generateDetailedReport() {
        const text = document.getElementById('inputText').value;
        const language = this.getSelectedLanguage();

        if (!text) {
            this.showNotification('没有可用的文本内容', 'warning');
            return;
        }

        try {
            // 显示加载状态
            this.showLoadingMessage('正在进行分段检测，请稍候...');

            // 调用分段检测API
            const response = await fetch(`${this.apiBaseUrl}/detect-paragraphs`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${window.AuthManager.getToken()}`
                },
                body: JSON.stringify({ text, language })
            });

            const result = await response.json();

            if (!result.success) {
                throw new Error(result.message || '分段检测失败');
            }

            // 生成PDF报告
            await this.generatePDFReport(result.data, text);

        } catch (error) {
            console.error('报告生成错误:', error);
            this.showNotification('网络连接超时，重新上传检测', 'error');
        } finally {
            this.hideLoadingMessage();
        }
    }

    showLoadingMessage(message) {
        // 创建加载提示
        const loadingDiv = document.createElement('div');
        loadingDiv.id = 'reportLoading';
        loadingDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 20px 40px;
            border-radius: 10px;
            z-index: 10000;
            text-align: center;
        `;
        loadingDiv.innerHTML = `
            <div style="margin-bottom: 10px;">📊</div>
            <div>${message}</div>
        `;
        document.body.appendChild(loadingDiv);
    }

    hideLoadingMessage() {
        const loadingDiv = document.getElementById('reportLoading');
        if (loadingDiv) {
            loadingDiv.remove();
        }
    }

    // 生成PDF报告
    async generatePDFReport(data, originalText) {
        try {
            console.log('🔍 开始生成PDF报告...');
            console.log('📊 检测数据:', data);
            console.log('📝 原始文本长度:', originalText?.length);

            // 获取用户信息
            const userInfo = await this.getUserInfo();
            console.log('👤 用户信息:', userInfo);

            const documentTitle = this.extractDocumentTitle(originalText);
            console.log('📄 文档标题:', documentTitle);

            // 检查关键数据
            if (!data.paragraphs || data.paragraphs.length === 0) {
                console.error('❌ 检测数据中没有段落信息！');
                this.showNotification('检测数据异常，无法生成报告', 'error');
                return;
            }

            if (!data.statistics) {
                console.error('❌ 检测数据中没有统计信息！');
                this.showNotification('统计数据异常，无法生成报告', 'error');
                return;
            }

            // 显示加载状态
            this.showNotification('正在生成PDF报告，请稍候...', 'info');

            // 混合方案：主方案html-to-pdfmake + 备用方案wkhtmltopdf
            console.log('🔄 开始PDF生成 - 混合方案');

            // 主方案：html-to-pdfmake (前端生成)
            try {
                console.log('📄 尝试主方案: html-to-pdfmake');
                await this.generatePDFWithPdfMake(data, userInfo, documentTitle, originalText);
                return;
            } catch (error) {
                console.error('❌ html-to-pdfmake方案失败:', error);
            }

            // 备用方案：wkhtmltopdf (后端生成)
            console.log('⚠️ 主方案失败，尝试备用方案: wkhtmltopdf');
            try {
                await this.generatePDFWithWkhtmltopdf(data, userInfo, documentTitle, originalText);
                return;
            } catch (error) {
                console.error('❌ wkhtmltopdf备用方案失败:', error);
            }

            // 最后备用：html2pdf (前端生成)
            console.log('⚠️ 备用方案失败，尝试最后方案: html2pdf');
            try {
                await this.generateClientSidePDF(data, userInfo, documentTitle, originalText);
            } catch (error) {
                console.error('❌ 所有PDF生成方案失败:', error);
                this.showNotification('PDF生成失败，请重试', 'error');
            }



        } catch (error) {
            console.error('PDF生成失败:', error);
            this.showNotification('网络连接超时，重新上传检测', 'error');
        }
    }

    // 备用PDF生成方法（使用浏览器原生打印）
    async generateFallbackPDF(html, userInfo) {
        try {
            console.log('🔄 使用浏览器原生打印方案生成PDF...');

            // 生成文件名
            const filename = `AIGC检测报告_${userInfo.username}_${new Date().toISOString().split('T')[0]}`;

            // 创建新窗口用于打印
            const printWindow = window.open('', '_blank', 'width=800,height=600');

            if (!printWindow) {
                console.error('❌ 无法打开打印窗口，可能被浏览器阻止');
                this.showNotification('无法打开打印窗口，请允许弹窗后重试', 'error');
                return;
            }

            // 优化HTML内容用于打印
            const printHtml = this.optimizeHtmlForPrint(html, filename);

            // 写入HTML内容
            printWindow.document.write(printHtml);
            printWindow.document.close();

            // 等待内容加载完成
            printWindow.onload = () => {
                setTimeout(() => {
                    // 自动触发打印对话框
                    printWindow.print();

                    // 打印完成后关闭窗口
                    printWindow.onafterprint = () => {
                        printWindow.close();
                    };
                }, 500);
            };

            this.showNotification('请在打印对话框中选择"保存为PDF"', 'info');

        } catch (error) {
            console.error('❌ 打印方案失败:', error);

            // 提供备用下载方案
            this.showNotification('打印失败，为您提供HTML版本下载', 'warning');
            this.downloadHTMLReport(html, userInfo);
        }
    }

    // 优化HTML内容用于打印
    optimizeHtmlForPrint(html, filename) {
        return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${filename}</title>
    <style>
        @media print {
            @page {
                size: A4;
                margin: 15mm;
            }
            body {
                font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif;
                font-size: 12pt;
                line-height: 1.5;
                color: #000;
                background: white;
                margin: 0;
                padding: 0;
            }
            .no-print {
                display: none !important;
            }
            .page-break {
                page-break-before: always;
            }
            .header h1 {
                font-size: 18pt;
                margin-bottom: 10pt;
            }
            .section {
                margin-bottom: 15pt;
                break-inside: avoid;
            }
            .section h2 {
                font-size: 14pt;
                margin-bottom: 8pt;
            }
            .info-grid {
                display: table;
                width: 100%;
                border-collapse: collapse;
            }
            .info-item {
                display: table-cell;
                padding: 6pt;
                border: 1pt solid #ccc;
                width: 50%;
                vertical-align: top;
            }
            .paragraph-item {
                margin: 8pt 0;
                padding: 8pt;
                border-left: 3pt solid #ddd;
                break-inside: avoid;
            }
            .paragraph-item.ai {
                background-color: #f8f8f8;
                border-left-color: #dc3545;
            }
            .paragraph-item.human {
                background-color: #f0f8f0;
                border-left-color: #28a745;
            }
            .risk-badge {
                padding: 3pt 8pt;
                border-radius: 10pt;
                color: white;
                background-color: #dc3545;
                font-weight: bold;
            }
        }
        @media screen {
            body {
                font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif;
                line-height: 1.6;
                margin: 20px;
                color: #333;
                max-width: 800px;
                margin: 20px auto;
            }
            .print-hint {
                background: #e3f2fd;
                padding: 15px;
                border-radius: 5px;
                margin-bottom: 20px;
                text-align: center;
                border: 1px solid #2196f3;
            }
            .print-btn {
                background: #2196f3;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 14px;
                margin: 0 5px;
            }
            .print-btn:hover {
                background: #1976d2;
            }
        }
    </style>
</head>
<body>
    <div class="print-hint no-print">
        <h3>📄 PDF报告预览</h3>
        <p>请使用浏览器的打印功能保存为PDF：</p>
        <button class="print-btn" onclick="window.print()">🖨️ 打印/保存PDF</button>
        <button class="print-btn" onclick="window.close()">❌ 关闭窗口</button>
        <p><small>💡 提示：在打印对话框中选择"保存为PDF"即可生成PDF文件</small></p>
    </div>
    ${html}
</body>
</html>`;
    }



    // 备用HTML下载方案
    downloadHTMLReport(html, userInfo) {
        try {
            const filename = `AIGC检测报告_${userInfo.username}_${new Date().toISOString().split('T')[0]}.html`;
            const blob = new Blob([html], { type: 'text/html;charset=utf-8' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            this.showNotification('HTML报告下载成功，可用浏览器打开并打印为PDF', 'info');
        } catch (error) {
            console.error('❌ HTML下载失败:', error);
            this.showNotification('下载失败: ' + error.message, 'error');
        }
    }

    // 获取用户信息
    async getUserInfo() {
        try {
            const token = window.AuthManager.getToken();
            const response = await fetch('/api/auth/profile', {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const responseData = await response.json();
                // 处理 {success: true, user: {...}} 格式的响应
                const userData = responseData.user || responseData;
                return {
                    username: userData.username || userData.email || '用户',
                    email: userData.email || ''
                };
            }
        } catch (error) {
            console.error('获取用户信息失败:', error);
        }

        return { username: '用户', email: '' };
    }

    // 提取文档标题
    extractDocumentTitle(text) {
        // 尝试提取第一行作为标题
        const lines = text.split('\n').filter(line => line.trim());
        if (lines.length > 0) {
            const firstLine = lines[0].trim();
            // 如果第一行较短且不包含句号，可能是标题
            if (firstLine.length <= 50 && !firstLine.includes('。')) {
                return firstLine;
            }
        }

        // 否则使用前10个字符
        return text.substring(0, 10) + (text.length > 10 ? '...' : '');
    }

    // 创建报告HTML
    createReportHTML(data, originalText, userInfo, documentTitle) {
        const currentDate = new Date().toLocaleDateString('zh-CN');
        const coloredText = this.generateColoredText(data.paragraphs);

        return `
        <div style="font-family: 'Microsoft YaHei', Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 40px; background: white;">
            <!-- 报告头部 -->
            <div style="text-align: center; margin-bottom: 40px; border-bottom: 2px solid #ff6b35; padding-bottom: 20px;">
                <h1 style="color: #333; margin: 0 0 10px 0; font-size: 28px;">AIGC检测报告</h1>
                <p style="color: #666; margin: 0; font-size: 14px;">AI Generated Content Detection Report</p>
            </div>

            <!-- 基本信息 -->
            <div style="margin-bottom: 30px;">
                <h2 style="color: #333; font-size: 18px; margin-bottom: 15px; border-left: 4px solid #ff6b35; padding-left: 10px;">基本信息</h2>
                <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                        <td style="padding: 8px 0; color: #666; width: 120px;">文档标题：</td>
                        <td style="padding: 8px 0; color: #333; font-weight: 500;">${documentTitle}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; color: #666;">作者：</td>
                        <td style="padding: 8px 0; color: #333; font-weight: 500;">${userInfo.username}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; color: #666;">检测时间：</td>
                        <td style="padding: 8px 0; color: #333; font-weight: 500;">${currentDate}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; color: #666;">检测语言：</td>
                        <td style="padding: 8px 0; color: #333; font-weight: 500;">${data.language === 'zh' ? '中文' : '英文'}</td>
                    </tr>
                </table>
            </div>

            <!-- 检测统计 -->
            <div style="margin-bottom: 30px;">
                <h2 style="color: #333; font-size: 18px; margin-bottom: 15px; border-left: 4px solid #ff6b35; padding-left: 10px;">检测统计</h2>
                <div style="display: flex; justify-content: space-between; margin-bottom: 20px;">
                    <div style="text-align: center; flex: 1;">
                        <div style="font-size: 24px; font-weight: bold; color: #ff3b30;">${data.statistics.highRiskParagraphs}</div>
                        <div style="color: #666; font-size: 12px;">高风险段落 (≥80%)</div>
                    </div>
                    <div style="text-align: center; flex: 1;">
                        <div style="font-size: 24px; font-weight: bold; color: #ff9500;">${data.statistics.mediumRiskParagraphs}</div>
                        <div style="color: #666; font-size: 12px;">中风险段落 (50-80%)</div>
                    </div>
                    <div style="text-align: center; flex: 1;">
                        <div style="font-size: 24px; font-weight: bold; color: #34c759;">${data.statistics.lowRiskParagraphs}</div>
                        <div style="color: #666; font-size: 12px;">低风险段落 (<50%)</div>
                    </div>
                </div>
                <div style="text-align: center;">
                    <canvas id="reportChart" width="400" height="200"></canvas>
                </div>
            </div>

            <!-- 详细分析 -->
            <div style="margin-bottom: 30px;">
                <h2 style="color: #333; font-size: 18px; margin-bottom: 15px; border-left: 4px solid #ff6b35; padding-left: 10px;">详细分析</h2>
                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; line-height: 1.8;">
                    ${coloredText}
                </div>
            </div>

            <!-- 报告说明 -->
            <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee;">
                <h3 style="color: #333; font-size: 16px; margin-bottom: 10px;">说明：</h3>
                <ul style="color: #666; font-size: 14px; line-height: 1.6; margin: 0; padding-left: 20px;">
                    <li><span style="color: #ff3b30;">红色文本</span>：AI概率 ≥ 80%，高度疑似AI生成</li>
                    <li><span style="color: #ff9500;">橙色文本</span>：AI概率 50-80%，中等疑似AI生成</li>
                    <li><span style="color: #333;">黑色文本</span>：AI概率 < 50%，疑似人工创作</li>
                </ul>
                <p style="color: #999; font-size: 12px; margin-top: 15px; text-align: center;">
                    本报告由WriterPro AIGC检测系统生成 | 检测时间：${new Date().toLocaleString('zh-CN')}
                </p>
            </div>
        </div>
        `;
    }

    // 生成带颜色标记的文本
    generateColoredText(paragraphs) {
        return paragraphs.map((para, index) => {
            if (para.skipped) {
                return `<p style="color: #999; font-style: italic; margin: 10px 0;">[段落${index + 1}：内容过短，已跳过检测]</p>`;
            }

            if (para.error) {
                return `<p style="color: #ff3b30; margin: 10px 0;">[段落${index + 1}：检测失败] ${para.text}</p>`;
            }

            const aiPercentage = Math.round(para.ai_probability * 100);
            let color = '#333'; // 默认黑色

            if (aiPercentage >= 80) {
                color = '#ff3b30'; // 红色
            } else if (aiPercentage >= 50) {
                color = '#ff9500'; // 橙色
            }

            return `<p style="color: ${color}; margin: 15px 0; padding: 10px; border-left: 3px solid ${color}; background: ${color}10;">
                <strong>[段落${index + 1} - AI率: ${aiPercentage}%]</strong><br>
                ${para.text}
            </p>`;
        }).join('');
    }

    // 生成图表
    async generateCharts(container, statistics) {
        const canvas = container.querySelector('#reportChart');
        if (!canvas || typeof Chart === 'undefined') return;

        const ctx = canvas.getContext('2d');

        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['高风险段落', '中风险段落', '低风险段落'],
                datasets: [{
                    data: [
                        statistics.highRiskParagraphs,
                        statistics.mediumRiskParagraphs,
                        statistics.lowRiskParagraphs
                    ],
                    backgroundColor: ['#ff3b30', '#ff9500', '#34c759'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            font: {
                                size: 12
                            }
                        }
                    }
                }
            }
        });

        // 等待图表渲染完成
        await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // 主方案：使用html-to-pdfmake生成PDF
    async generatePDFWithPdfMake(data, userInfo, documentTitle, originalText) {
        console.log('🎯 使用html-to-pdfmake生成PDF');

        // 生成HTML内容
        const htmlContent = this.createReportHTML(data, userInfo, documentTitle);

        // 转换为PDFMake格式
        const pdfContent = htmlToPdfmake(htmlContent, {
            defaultStyles: {
                h1: { fontSize: 24, bold: true, marginBottom: 10, color: '#333' },
                h2: { fontSize: 20, bold: true, marginBottom: 8, color: '#333' },
                h3: { fontSize: 18, bold: true, marginBottom: 6, color: '#333' },
                p: { fontSize: 14, marginBottom: 5, lineHeight: 1.6 },
                strong: { bold: true },
                em: { italics: true }
            },
            removeExtraBlanks: true,
            tableAutoSize: true
        });

        // 创建PDF文档定义
        const docDefinition = {
            content: pdfContent,
            defaultStyle: {
                font: 'Roboto',
                fontSize: 14,
                lineHeight: 1.6
            },
            styles: {
                header: {
                    fontSize: 24,
                    bold: true,
                    alignment: 'center',
                    marginBottom: 20
                },
                subheader: {
                    fontSize: 18,
                    bold: true,
                    marginBottom: 10
                }
            },
            pageMargins: [40, 60, 40, 60]
        };

        // 生成并下载PDF
        const fileName = `AIGC检测报告_${userInfo.username}_${new Date().toISOString().split('T')[0]}.pdf`;
        pdfMake.createPdf(docDefinition).download(fileName);

        console.log('✅ html-to-pdfmake PDF生成成功');
        this.showNotification('PDF报告生成成功！', 'success');
    }

    // 备用方案：调用后端wkhtmltopdf
    async generatePDFWithWkhtmltopdf(data, userInfo, documentTitle, originalText) {
        console.log('🔄 调用后端wkhtmltopdf生成API...');

        const response = await fetch('/api/pdf/generate-wkhtmltopdf', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${window.AuthManager.getToken()}`
            },
            body: JSON.stringify({
                detectionData: data,
                userInfo: userInfo,
                documentTitle: documentTitle,
                originalText: originalText
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        console.log('✅ wkhtmltopdf PDF生成成功');
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `AIGC检测报告_${userInfo.username}_${new Date().toISOString().split('T')[0]}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        this.showNotification('PDF报告生成成功！', 'success');
    }

    // 生成报告HTML内容
    createReportHTML(data, userInfo, documentTitle) {
        const currentDate = new Date().toLocaleDateString('zh-CN');
        const { paragraphs, statistics, language } = data;

        // 生成带颜色标记的段落
        const coloredParagraphs = paragraphs.map((para, index) => {
            const aiPercentage = Math.round((para.ai_probability || 0) * 100);
            let colorClass = 'low-risk';
            let riskLabel = '低风险';

            if (aiPercentage >= 80) {
                colorClass = 'high-risk';
                riskLabel = '高风险';
            } else if (aiPercentage >= 50) {
                colorClass = 'medium-risk';
                riskLabel = '中风险';
            }

            return `
                <div class="paragraph ${colorClass}">
                    <div class="paragraph-header">
                        <strong>段落 ${index + 1} - ${riskLabel} (AI概率: ${aiPercentage}%)</strong>
                    </div>
                    <div class="paragraph-content">${para.text || '段落内容为空'}</div>
                </div>
            `;
        }).join('');

        return `
            <div class="report-container">
                <div class="report-header">
                    <h1>AIGC检测报告</h1>
                    <div class="report-info">
                        <p><strong>文档标题：</strong>${documentTitle}</p>
                        <p><strong>检测用户：</strong>${userInfo.username || '未知用户'}</p>
                        <p><strong>检测时间：</strong>${currentDate}</p>
                        <p><strong>检测语言：</strong>${language === 'zh' ? '中文' : '英文'}</p>
                    </div>
                </div>

                <div class="statistics-section">
                    <h2>检测统计</h2>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-value">${statistics.totalParagraphs || 0}</div>
                            <div class="stat-label">总段落数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${statistics.highRiskParagraphs || 0}</div>
                            <div class="stat-label">高风险段落</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${statistics.mediumRiskParagraphs || 0}</div>
                            <div class="stat-label">中风险段落</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${statistics.lowRiskParagraphs || 0}</div>
                            <div class="stat-label">低风险段落</div>
                        </div>
                    </div>
                </div>

                <div class="paragraphs-section">
                    <h2>详细分析</h2>
                    ${coloredParagraphs}
                </div>

                <style>
                    .report-container {
                        font-family: "Microsoft YaHei", "SimHei", Arial, sans-serif;
                        max-width: 800px;
                        margin: 0 auto;
                        padding: 20px;
                        line-height: 1.6;
                        color: #333;
                    }
                    .report-header {
                        text-align: center;
                        margin-bottom: 30px;
                        border-bottom: 2px solid #ff6b35;
                        padding-bottom: 20px;
                    }
                    .report-header h1 {
                        color: #333;
                        margin-bottom: 20px;
                        font-size: 28px;
                    }
                    .report-info p {
                        margin: 5px 0;
                        font-size: 16px;
                    }
                    .statistics-section, .paragraphs-section {
                        margin-bottom: 30px;
                    }
                    .statistics-section h2, .paragraphs-section h2 {
                        color: #333;
                        border-bottom: 2px solid #ff6b35;
                        padding-bottom: 8px;
                        margin-bottom: 20px;
                    }
                    .stats-grid {
                        display: flex;
                        justify-content: space-around;
                        background: #f8f9fa;
                        padding: 20px;
                        border-radius: 8px;
                        margin-bottom: 20px;
                    }
                    .stat-item {
                        text-align: center;
                    }
                    .stat-value {
                        font-size: 24px;
                        font-weight: bold;
                        color: #ff6b35;
                        margin-bottom: 5px;
                    }
                    .stat-label {
                        color: #666;
                        font-size: 14px;
                    }
                    .paragraph {
                        margin: 15px 0;
                        padding: 15px;
                        border-radius: 8px;
                        border-left: 4px solid;
                    }
                    .paragraph.high-risk {
                        border-left-color: #dc3545;
                        background-color: #f8d7da;
                    }
                    .paragraph.medium-risk {
                        border-left-color: #fd7e14;
                        background-color: #fff3cd;
                    }
                    .paragraph.low-risk {
                        border-left-color: #198754;
                        background-color: #d1e7dd;
                    }
                    .paragraph-header {
                        font-weight: bold;
                        margin-bottom: 8px;
                    }
                    .paragraph-content {
                        line-height: 1.8;
                    }
                </style>
            </div>
        `;
    }
}

// 页面加载完成后初始化，确保所有脚本都已加载
document.addEventListener('DOMContentLoaded', () => {
    // 延迟初始化，确保AuthManager完全准备就绪
    setTimeout(() => {
        console.log('开始初始化AIGC检测页面...');
        new AigcDetection();
    }, 100);
});
